{"name": "minimind", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@stripe/stripe-js": "^7.9.0", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.56.0", "@vercel/analytics": "^1.5.0", "framer-motion": "^12.9.2", "lucide-react": "^0.503.0", "next": "15.3.1", "openai": "^4.96.2", "react": "^19.0.0", "react-dom": "^19.0.0", "stripe": "^18.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}