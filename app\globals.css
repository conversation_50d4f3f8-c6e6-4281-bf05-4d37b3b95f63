@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Indie+Flower&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Coming+Soon&display=swap');


:root {
  --background: #ffffff;
  --foreground: #171717;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-fredoka), sans-serif;
}

.kid-font {
  font-family: var(--font-coming-soon), cursive;
}

.other-font {
  font-family: var(--font-fredoka), sans-serif;
}

@layer utilities {
  .animate-bounce {
    animation: bounce 0.6s infinite ease-in-out;
  }

  .delay-\[150ms\] {
    animation-delay: 150ms;
  }

  .delay-\[300ms\] {
    animation-delay: 300ms;
  }
}

p.jon {
  display: flex;
  justify-content: center;
  margin-top: 45px
}

.kofi img {
  height: 35px;
  margin: 15px auto 0;
  bottom: 30px;
  display: flex;
}

.logo {
  width: 50px;
  height: 50px;
  display: inline-block;
}

h1 {
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-size: 1.2rem;
}

h2, h3, h4 {
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-size: 1rem;
}

a:hover, button:hover {
  cursor: pointer;
}

